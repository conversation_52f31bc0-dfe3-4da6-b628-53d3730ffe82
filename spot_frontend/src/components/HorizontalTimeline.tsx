import React from 'react';

// Add custom scrollbar styles
const scrollbarStyles = `
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
`;

export interface TimelineItem {
  id: string;
  period: string; // e.g., "Q1 '24"
  title: string;
  status: 'completed' | 'in-progress' | 'upcoming';
  avatar?: string; // URL or emoji
  timeIndicator?: string; // e.g., "02:05"
  description?: string;
}

interface HorizontalTimelineProps {
  items: TimelineItem[];
  className?: string;
}

const HorizontalTimeline: React.FC<HorizontalTimelineProps> = ({ items, className = '' }) => {
  // Inject custom styles
  React.useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.textContent = scrollbarStyles;
    document.head.appendChild(styleElement);
    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500 border-green-400';
      case 'in-progress':
        return 'bg-yellow-500 border-yellow-400';
      case 'upcoming':
        return 'bg-gray-500 border-gray-400';
      default:
        return 'bg-gray-500 border-gray-400';
    }
  };

  const getStatusGlow = (status: string) => {
    switch (status) {
      case 'completed':
        return 'shadow-green-500/30';
      case 'in-progress':
        return 'shadow-yellow-500/30';
      case 'upcoming':
        return 'shadow-gray-500/20';
      default:
        return 'shadow-gray-500/20';
    }
  };

  return (
    <div className={`w-full bg-[#141416] ${className}`}>
      <div className="relative w-full overflow-x-auto scrollbar-hide">
        <div className="min-w-max px-4 sm:px-8 py-8">
          {/* Timeline Container */}
          <div className="relative" style={{ minWidth: `${Math.max(items.length * 140, 1000)}px` }}>

            {/* Horizontal Timeline Line */}
            <div className="absolute top-20 left-8 right-8 h-1 bg-gradient-to-r from-green-500/80 via-yellow-500/80 to-gray-500/80 rounded-full z-0">
              <div className="absolute inset-0 bg-gradient-to-r from-green-500/20 via-yellow-500/20 to-gray-500/20 rounded-full blur-sm"></div>
            </div>

            {/* Timeline Items */}
            <div className="relative flex justify-between items-start z-10 px-8">
              {items.map((item, index) => (
                <div key={item.id} className="flex flex-col items-center group cursor-pointer">

                  {/* Period Label (Above) */}
                  <div className="mb-4 text-center">
                    <div className="text-white font-bold text-sm mb-1 group-hover:text-[#7FFFD4] transition-colors">
                      {item.period}
                    </div>
                    {item.title && (
                      <div className="text-gray-400 text-xs max-w-20 leading-tight group-hover:text-gray-300 transition-colors">
                        {item.title}
                      </div>
                    )}
                  </div>

                  {/* Milestone Circle/Avatar */}
                  <div className="relative mb-4">
                    <div
                      className={`w-12 h-12 sm:w-14 sm:h-14 rounded-full border-3 border-[#141416] ${getStatusColor(item.status)} ${getStatusGlow(item.status)} shadow-xl flex items-center justify-center transition-all duration-300 group-hover:scale-110 group-hover:shadow-2xl`}
                    >
                      {item.avatar ? (
                        typeof item.avatar === 'string' && item.avatar.startsWith('http') ? (
                          <img
                            src={item.avatar}
                            alt={item.title}
                            className="w-8 h-8 sm:w-10 sm:h-10 rounded-full object-cover"
                          />
                        ) : (
                          <span className="text-lg sm:text-xl">{item.avatar}</span>
                        )
                      ) : (
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      )}
                    </div>

                    {/* Status Indicator Ring */}
                    {item.status === 'in-progress' && (
                      <div className="absolute inset-0 rounded-full border-2 border-yellow-400/60 animate-pulse"></div>
                    )}

                    {/* Completion Checkmark */}
                    {item.status === 'completed' && (
                      <div className="absolute -top-1 -right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </div>

                  {/* Time Indicator (Below) */}
                  {item.timeIndicator && (
                    <div className="text-gray-500 text-xs font-mono mb-2 group-hover:text-gray-400 transition-colors">
                      {item.timeIndicator}
                    </div>
                  )}

                  {/* Description (Optional) */}
                  {item.description && (
                    <div className="text-gray-400 text-xs text-center max-w-24 leading-tight group-hover:text-gray-300 transition-colors">
                      {item.description}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Mobile Scroll Indicator */}
        <div className="flex justify-center mt-2 sm:hidden">
          <div className="flex space-x-1">
            {items.map((_, index) => (
              <div key={index} className="w-1.5 h-1.5 bg-gray-600 rounded-full"></div>
            ))}
          </div>
          <div className="text-gray-500 text-xs mt-2">Scroll to see more</div>
        </div>
      </div>
    </div>
  );
};

export default HorizontalTimeline;
