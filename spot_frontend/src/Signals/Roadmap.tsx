import React from 'react';

interface ActivityEvent {
  id: string;
  timestamp: string;
  title: string;
  frequency: string;
  status: 'high' | 'medium' | 'low';
  icon: string;
  category: 'trade' | 'signal' | 'alert';
  description: string;
}

const activityData: ActivityEvent[] = [
  {
    id: '1',
    timestamp: 'Yesterday 17:44',
    title: 'Smart Money Alert',
    frequency: '40x',
    status: 'high',
    icon: '🧠',
    category: 'alert',
    description: 'Large whale movement detected'
  },
  {
    id: '2',
    timestamp: 'Yesterday 19:22',
    title: 'KOL Signal',
    frequency: '12x',
    status: 'medium',
    icon: '📢',
    category: 'signal',
    description: 'Crypto influencer buy signal'
  },
  {
    id: '3',
    timestamp: 'Today 02:15',
    title: 'Trade Execution',
    frequency: '8x',
    status: 'high',
    icon: '⚡',
    category: 'trade',
    description: 'Automated trade completed'
  },
  {
    id: '4',
    timestamp: 'Today 02:34',
    title: 'Portfolio Alert',
    frequency: '3x',
    status: 'low',
    icon: '📊',
    category: 'alert',
    description: 'Portfolio rebalancing triggered'
  },
  {
    id: '5',
    timestamp: 'Today 05:18',
    title: 'Market Signal',
    frequency: '25x',
    status: 'high',
    icon: '📈',
    category: 'signal',
    description: 'Bullish market trend detected'
  },
  {
    id: '6',
    timestamp: 'Today 08:45',
    title: 'Copy Trade',
    frequency: '6x',
    status: 'medium',
    icon: '👥',
    category: 'trade',
    description: 'Following smart wallet trades'
  },
  {
    id: '7',
    timestamp: 'Today 11:30',
    title: 'Risk Alert',
    frequency: '2x',
    status: 'medium',
    icon: '⚠️',
    category: 'alert',
    description: 'High volatility warning'
  },
  {
    id: '8',
    timestamp: 'Today 14:12',
    title: 'DeFi Signal',
    frequency: '15x',
    status: 'high',
    icon: '💎',
    category: 'signal',
    description: 'Yield farming opportunity'
  }
];

const Roadmap = () => {
  // Generate gamified progress nodes with character data
  const progressNodes = Array.from({ length: 8 }, (_, index) => {
    const baseTime = new Date();
    baseTime.setHours(baseTime.getHours() - (7 - index) * 3); // 3-hour intervals for 24h period
    const timestamp = baseTime.toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit' 
    });
    
    const characters = ['🚀', '💎', '⚡', '🎯', '🔥', '💰', '🌟', '🎮'];
    const multipliers = ['2x', '5x', '3x', '10x', '7x', '15x', '4x', '20x'];
    const points = [100, 250, 150, 500, 350, 750, 200, 1000];
    const isActive = index === 5; // Current active node
    const isCompleted = index < 5;
    
    return {
      id: index,
      timestamp,
      character: characters[index],
      multiplier: multipliers[index],
      points: points[index],
      isActive,
      isCompleted,
      size: isActive ? 'large' : isCompleted ? 'medium' : 'small'
    };
  });

  const currentTime = new Date().toLocaleTimeString('en-US', { 
    hour12: false, 
    hour: '2-digit', 
    minute: '2-digit' 
  });

  return (
    <div className="w-full space-y-2">


      {/* Timeline Container - App Theme */}
      <div className="w-full bg-gradient-to-br from-[#141416] via-[#181C20]/90 to-[#141416] rounded-lg p-2 shadow-[0_6px_15px_-4px_rgba(0,0,0,0.4)] border border-[#181C20]/50 backdrop-blur-xl">
        <div className="relative">
          <div className="px-3 py-4">
            {/* Elegant Timeline Track */}
            <div className="relative" style={{ height: '128px' }}>
              
              {/* Elegant Timeline Line */}
              <div className="absolute top-1/2 left-0 right-0 transform -translate-y-1/2 z-0">
                <div className="w-full h-1 bg-gradient-to-r from-emerald-500/30 via-green-400/50 to-emerald-500/30 rounded-full relative">
                  {/* Refined dotted pattern */}
                  <div className="absolute inset-0 bg-gradient-to-r from-emerald-400/90 via-green-400 to-emerald-400/90 rounded-full" 
                       style={{
                         backgroundImage: 'radial-gradient(circle, transparent 30%, currentColor 30%)',
                         backgroundSize: '24px 4px',
                         backgroundRepeat: 'repeat-x'
                       }}></div>
                  {/* Subtle glow effect */}
                  <div className="absolute inset-0 bg-emerald-400/60 rounded-full blur-xl opacity-25"></div>
                  {/* Additional shimmer effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded-full animate-pulse"></div>
                </div>
              </div>

              {/* Progress Nodes */}
              <div className="relative flex justify-between items-center">
                {progressNodes.map((node, index) => (
                  <div key={node.id} className="flex flex-col items-center group relative">
                    
                    {/* Compact Multiplier Badge */}
                    <div className="mb-1 relative z-30">
                      <div className={`bg-gradient-to-r from-amber-400 via-yellow-400 to-orange-400 text-slate-900 px-1 py-0.5 rounded text-xs font-semibold shadow-sm transform transition-all duration-500 ${
                        node.isActive ? 'scale-110 animate-pulse' : 'group-hover:scale-105'
                      }`}>
                        <span className="font-bold text-xs">{node.multiplier}</span>
                        {/* Refined glow */}
                        <div className="absolute inset-0 bg-gradient-to-r from-amber-400/50 via-yellow-400/60 to-orange-400/50 rounded blur-sm opacity-30 -z-10"></div>
                        {/* Subtle inner glow */}
                        <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-white/10 rounded opacity-40"></div>
                      </div>
                    </div>

                    {/* Elegant Character Node */}
                    <div className="relative mb-4 z-20">
                      {/* Refined node with elegant styling */}
                      <div className={`rounded-full border-2 flex items-center justify-center transition-all duration-700 group-hover:scale-110 backdrop-blur-sm ${
                        node.size === 'large' ? 'w-12 h-12 border-amber-300/80 bg-gradient-to-br from-amber-400/20 via-yellow-400/25 to-orange-400/20 shadow-[0_15px_25px_-8px_rgba(251,191,36,0.4)]' :
                        node.size === 'medium' ? 'w-10 h-10 border-emerald-300/70 bg-gradient-to-br from-emerald-400/20 via-green-400/25 to-teal-400/20 shadow-[0_10px_20px_-5px_rgba(52,211,153,0.35)]' :
                        'w-8 h-8 border-gray-400/50 bg-gradient-to-br from-[#181C20]/40 via-gray-500/20 to-[#181C20]/40 shadow-[0_8px_15px_-3px_rgba(107,114,128,0.25)]'
                      }`}>
                        {/* Elegant Character Emoji */}
                        <span className={`filter drop-shadow-2xl transition-all duration-500 ${
                          node.size === 'large' ? 'text-2xl' :
                          node.size === 'medium' ? 'text-xl' : 'text-lg'
                        }`}>
                          {node.character}
                        </span>
                        
                        {/* Refined outer glow */}
                        <div className={`absolute inset-0 rounded-full blur-2xl opacity-30 transition-all duration-700 ${
                          node.isActive ? 'bg-amber-400/80 animate-pulse' :
                          node.isCompleted ? 'bg-emerald-400/70' : 'bg-gray-500/40'
                        }`}></div>
                        
                        {/* Subtle inner highlight */}
                        <div className="absolute inset-2 rounded-full bg-gradient-to-br from-white/10 to-transparent opacity-60"></div>
                      </div>
                      
                      {/* Elegant points indicator */}
                      {node.isCompleted && (
                        <div className="absolute -top-0.5 -right-0.5 bg-gradient-to-r from-emerald-400 via-green-400 to-teal-400 text-white text-xs font-medium px-0.5 py-0 rounded text-xs shadow-sm border border-emerald-300/50 z-30 backdrop-blur-sm">
                          <span className="drop-shadow-sm text-xs leading-tight">+{node.points}</span>
                        </div>
                      )}
                      
                      {/* Refined active pulse effect */}
                      {node.isActive && (
                        <div className="absolute inset-0 rounded-full border border-amber-300/60 animate-ping opacity-40"></div>
                      )}
                      
                      {/* Additional elegant pulse ring */}
                      {node.isActive && (
                        <div className="absolute -inset-2 rounded-full border border-amber-200/30 animate-pulse opacity-20"></div>
                      )}
                    </div>

                    {/* Timestamp - App Theme */}
                    <div className="text-center mt-3">
                      <span className="text-xs text-gray-300 font-light tracking-wide bg-[#181C20]/80 px-1 py-0.5 rounded border border-[#181C20]/60 backdrop-blur-sm shadow-lg">
                        {node.timestamp}
                      </span>
                    </div>

                    {/* Hover Tooltip - App Theme */}
                    <div className="absolute -top-24 left-1/2 transform -translate-x-1/2 bg-gradient-to-br from-[#141416]/95 to-[#181C20]/95 border border-[#181C20]/60 rounded-2xl p-3 min-w-40 opacity-0 group-hover:opacity-100 transition-all duration-500 pointer-events-none z-40 shadow-2xl backdrop-blur-md group-hover:translate-y-1">
                      <div className="text-gray-100 font-medium text-sm mb-1">{node.character} Milestone</div>
                      <div className="text-amber-300 text-xs mb-1 font-medium">{node.timestamp} • {node.multiplier}</div>
                      <div className="text-emerald-300 text-xs mb-2">Points: <span className="font-semibold">+{node.points}</span></div>
                      <div className={`inline-block px-1.5 py-0.5 rounded text-xs font-bold transition-all duration-300 ${
                        node.isActive ? 'bg-gradient-to-r from-amber-400 to-yellow-400 text-slate-900 shadow-sm' :
                        node.isCompleted ? 'bg-gradient-to-r from-emerald-400 to-green-400 text-white shadow-sm' : 'bg-gradient-to-r from-slate-500 to-slate-600 text-slate-200'
                      }`}>
                        {node.isActive ? 'ACTIVE' : node.isCompleted ? 'COMPLETED' : 'LOCKED'}
                      </div>
                      {/* Tooltip Arrow - App Theme */}
                      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-full">
                        <div className="border-4 border-transparent border-t-[#141416]/95"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>


        </div>
      </div>


    </div>
  );
};

export default Roadmap;
