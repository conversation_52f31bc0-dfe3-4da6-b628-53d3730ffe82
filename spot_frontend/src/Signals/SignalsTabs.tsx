import React from "react";
import Roadmap from "./Roadmap";
import SmartSignals from "./SmartSignals";

const SignalsTabs = () => {
  return (
    <div className="h-full flex flex-col space-y-6">
      {/* Time Map Section */}
      <div className="flex-shrink-0">
        <div className="flex items-center gap-2 mb-4">
          <svg className="w-5 h-5 text-[#7FFFD4]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <h2 className="text-lg font-semibold text-white">Time Map</h2>
        </div>
        <Roadmap />
      </div>

      {/* Smart Signals Section */}
      <div className="flex-1">
        <div className="flex items-center gap-2 mb-4">
          <svg className="w-5 h-5 text-[#7FFFD4]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
          <h2 className="text-lg font-semibold text-white">Smart Signals</h2>
        </div>
        <SmartSignals />
      </div>
    </div>
  );
};

export default SignalsTabs;
