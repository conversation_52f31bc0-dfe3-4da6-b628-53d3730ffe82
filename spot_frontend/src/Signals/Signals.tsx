import React from 'react';
import Navbar from '@/Home/Navbar/Navbar';
import SignalsTabs from './SignalsTabs';

const Signals = () => {
  return (
    <div className="min-h-screen flex flex-col bg-[#141416]">
      <Navbar />

      <main className="flex-1 overflow-hidden">
        <div className="w-full px-4 sm:px-6 lg:px-8 h-full">
          <div className="py-6 h-full w-full">
            {/* Header */}
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-white mb-2">Signals</h1>
              <p className="text-gray-400 text-sm">Track roadmap progress and smart money signals</p>
            </div>

            {/* Signals Tabs */}
            <div className="w-full">
              <SignalsTabs />
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Signals;
